-- Add missing bulk generation tracking columns to company_campaigns
ALTER TABLE public.company_campaigns
  ADD COLUMN IF NOT EXISTS bulk_generation_status text,
  ADD COLUMN IF NOT EXISTS bulk_generation_progress integer,
  ADD COLUMN IF NOT EXISTS bulk_generation_error text,
  ADD COLUMN IF NOT EXISTS tasks_generated_count integer,
  ADD COLUMN IF NOT EXISTS tasks_content_generated_count integer,
  ADD COLUMN IF NOT EXISTS bulk_generation_started_at bigint,
  ADD COLUMN IF NOT EXISTS bulk_generation_completed_at bigint;

-- Add missing content generation tracking columns to company_content
ALTER TABLE public.company_content
  ADD COLUMN IF NOT EXISTS content_generation_status text,
  ADD COLUMN IF NOT EXISTS content_generation_error text,
  ADD COLUMN IF NOT EXISTS content_generation_attempts integer,
  ADD COLUMN IF NOT EXISTS auto_generated boolean,
  ADD COLUMN IF NOT EXISTS content_quality_score integer;
