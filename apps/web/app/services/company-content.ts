'use server'
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { CompanyContent } from '~/types/company-content';
import type { Database } from '~/lib/database.types';

const server = getSupabaseServerClient();

export async function createCompanyContent(
  content: Partial<CompanyContent> & {
    company_id: string;
    campaign_id: string;
    idea_id: string;
    content_type: string;
    language: string;
    content: string;
  },
) {
  const processedContent = { ...content };
  if (processedContent.content_editor_template) {
    processedContent.content_editor_template = JSON.parse(JSON.stringify(processedContent.content_editor_template));
  }

  const { data: companyContent, error } = await server
    .from('company_content')
    .insert(processedContent as Database['public']['Tables']['company_content']['Insert'])
    .select()
    .single();

  if (error) throw error;

  return companyContent;
}

export async function getCompanyContent(campaignId: string) {
  console.log("GETTING COMPANY CONTENT", {campaignId});
  
  const { data, error } = await server
    .from('company_content')
    .select('*')
    .eq('campaign_id', campaignId)
    .order('created_at', { ascending: false });

  if (error) {
    throw error;
  }

  return data as CompanyContent[];
}

export async function getCompanyContentById(contentId: string) {
  console.log("GETTING COMPANY CONTENT BY ID", {contentId});
  
  const { data, error } = await server
    .from('company_content')
    .select('*')
    .eq('id', contentId)
    .order('created_at', { ascending: false })
    .single();

  if (error) {
    throw error;
  }

  return data as CompanyContent;
}

  /**
   * Saves content tasks to the company_content table
   */
  export async function saveContentTasks(
    tasks: any[],
    campaignId: string,
    ideaId: string,
    companyId: string,
  ): Promise<any[]> {

    const tasksToInsert = tasks.map(task => ({
      campaign_id: campaignId,
      idea_id: ideaId, // Using campaign ID as idea ID since they're 1:1 in this case
      content_type: task.content_type,
      language: task.language,
      task_id: task.id,
      task_title: task.title,
      task_description: task.content.description,
      channel: task.channel,
      status: task.status ? task.status.toLowerCase() : 'to do',
      has_image: task.hasVisualContent,
      visual_description: task.content.visualDescription || null,
      company_id: companyId,
      scheduled_publishing_time: task.scheduled_publishing_time.date ? 
        new Date(task.scheduled_publishing_time.date.split('/').reverse().join('-')).toISOString() : 
        null,
      is_scheduled: false,
      is_posted: false,
      is_draft: true, // Default to draft
    //   content: task.content.body,
    }));

    const { error: contentError } = await server
      .from('company_content')
      .insert(tasksToInsert);

    if (contentError) {
      throw new Error(`Failed to save content tasks: ${contentError.message}`);
    }
    return tasksToInsert;
  }

/**
 * Updates a company content record with the provided fields
 */
export async function updateCompanyContent(
  id: string | undefined,
  updates: Partial<CompanyContent>
): Promise<void> {
  try {
    if (!id) {
      console.error("Cannot update company content: ID is undefined or null");
      throw new Error("Failed to update company content: ID is undefined or null");
    }
    
    const processedUpdates = { ...updates };
    if (processedUpdates.status) {
      processedUpdates.status = processedUpdates.status.toLowerCase();
    }
    
    if (processedUpdates.content_editor_template) {
      processedUpdates.content_editor_template = JSON.parse(JSON.stringify(processedUpdates.content_editor_template));
    }
    
    console.log("UPDATING COMPANY CONTENT", {id, updates: processedUpdates});
    const { data, error } = await server
      .from('company_content')
      .update(processedUpdates as Database['public']['Tables']['company_content']['Update'])
      .eq('id', id);
    console.log("UPDATED COMPANY CONTENT", {data, error});
    
    if (error) {
      console.error("Error updating company content:", error);
      throw new Error(`Failed to update company content: ${error.message}`);
    }
  } catch (err) {
    console.error("Exception in updateCompanyContent:", err);
    throw err;
  }
}

/**
 * Deletes a company content record
 */
export async function deleteCompanyContent(id: string): Promise<void> {
  console.log("DELETING COMPANY CONTENT", {id});
  
  const { data, error } = await server
    .from('company_content')
    .delete()
    .eq('id', id);
  console.log("DELETED COMPANY CONTENT", {data, error});
  if (error) {
    throw new Error(`Failed to delete company content: ${error.message}`);
  }
}

